# RDKShell Architecture

## Architectural Overview

RDKShell follows a modular, event-driven architecture that separates concerns between application management, display composition, input handling, and system communication. The architecture is designed to provide high performance while maintaining flexibility for different deployment scenarios and hardware configurations.

## Core Components

### Main Application Loop
The central component of RDKShell is the main application loop implemented in `rdkshell.cpp`. This component orchestrates all system operations through a carefully timed rendering loop that maintains consistent frame rates while processing input events, updating application states, and managing system resources. The main loop operates at a configurable frame rate (default 40 FPS) and coordinates between all other subsystems.

### Compositor Controller
The CompositorController serves as the primary interface for all application management operations. It maintains the master list of active applications, manages their z-order relationships, handles focus management, and coordinates display composition operations. This component implements the core business logic for window management including bounds calculation, visibility control, opacity management, and animation coordination.

### Essos Instance Manager
The EssosInstance component provides the abstraction layer between RDKShell and the underlying windowing system. It handles the creation and management of Wayland surfaces, manages OpenGL ES contexts, and provides the rendering surface for the compositor. This component enables RDKShell to work with different windowing systems and graphics hardware through a consistent interface.

### RDK Compositor System
The RdkCompositor hierarchy (RdkCompositor, RdkCompositorSurface, RdkCompositorNested) implements the actual display composition logic. These components handle the low-level details of surface management, texture handling, and rendering operations. They coordinate with the graphics hardware to ensure efficient composition of multiple application surfaces into the final display output.

### Input Management System
The input management system consists of multiple components working together to provide comprehensive input handling. The LinuxInput component handles low-level input device management, while LinuxKeys provides key code mapping and translation. The system supports both physical input devices and virtual input generation, with sophisticated routing capabilities that allow applications to register for specific key combinations regardless of focus state.

### Communication Subsystem
RDKShell implements multiple communication protocols through a pluggable architecture. The ServerMessageHandler provides JSON-RPC over socket-based IPC, while the MessageHandler implements WebSocket-based communication. Both systems use the same underlying CompositorController APIs, ensuring consistent behavior across different communication methods.

## Component Interaction Flow

```mermaid
graph TB
    A[Main Application Loop] --> B[Compositor Controller]
    A --> C[Essos Instance]
    A --> D[Input Manager]
    A --> E[Communication Handlers]
    
    B --> F[RDK Compositor]
    B --> G[Animation System]
    B --> H[Application Registry]
    
    C --> I[Wayland Surface Manager]
    C --> J[OpenGL Context]
    
    D --> K[Linux Input Handler]
    D --> L[Key Mapping System]
    D --> M[Event Router]
    
    E --> N[JSON-RPC Handler]
    E --> O[WebSocket Handler]
    E --> P[Socket Communication]
    
    F --> Q[Surface Composition]
    F --> R[Texture Management]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

## Data Flow Architecture

### Application Lifecycle Data Flow
When an application is launched, the request flows through the communication layer to the CompositorController, which coordinates with the EssosInstance to create the necessary Wayland surfaces. The RdkCompositor system then manages the ongoing rendering and composition of the application's visual output. State changes are propagated back through the system to update client applications and maintain consistency.

### Input Event Processing Flow
Input events originate from the LinuxInput system, which captures raw input from various devices. These events are processed through the key mapping system to translate hardware-specific codes into standardized key codes. The CompositorController then applies the configured input routing rules to determine which applications should receive each event, supporting both focused application delivery and global key intercepts.

### Rendering and Composition Flow
The rendering pipeline begins with the main application loop triggering a frame update. The CompositorController coordinates with all active RdkCompositor instances to update their visual state, including position, size, opacity, and any active animations. The EssosInstance provides the OpenGL context and manages the final composition to the display surface.

## Initialization Sequence

```mermaid
sequenceDiagram
    participant Main as Main Process
    participant RDK as RdkShell Core
    participant Essos as Essos Instance
    participant Comp as Compositor Controller
    participant Input as Input System
    participant Comm as Communication
    
    Main->>RDK: initialize()
    RDK->>RDK: Load configuration
    RDK->>RDK: Setup key mappings
    RDK->>RDK: Configure memory monitoring
    RDK->>Essos: Initialize windowing system
    Essos->>Essos: Create OpenGL context
    Essos->>Essos: Setup Wayland surfaces
    RDK->>Comp: Initialize compositor
    Comp->>Comp: Setup application registry
    RDK->>Input: Initialize input handling
    Input->>Input: Configure input devices
    Input->>Input: Setup key routing
    RDK->>Comm: Start communication handlers
    Comm->>Comm: Initialize IPC channels
    RDK->>Main: Initialization complete
    Main->>RDK: run()
    RDK->>RDK: Enter main loop
```

## Memory and Resource Management

### Memory Monitoring Architecture
RDKShell implements a sophisticated memory monitoring system that operates in a separate thread to avoid impacting the main rendering loop. The system continuously monitors system RAM, swap usage, and application-specific memory consumption. Configurable thresholds trigger notifications to applications and system components, enabling proactive resource management.

### Resource Allocation Strategy
The architecture employs a careful resource allocation strategy that balances performance with memory efficiency. Graphics resources are managed through the OpenGL context with automatic cleanup when applications are terminated. The system maintains resource pools for commonly used objects to minimize allocation overhead during normal operations.

## Extension and Plugin Architecture

### Westeros Plugin Integration
RDKShell supports Westeros plugins that can extend the core functionality with platform-specific features. The plugin system is designed to maintain API compatibility while allowing for hardware-specific optimizations and additional capabilities.

### Built-in Extension System
The architecture includes built-in extensions for client control and extended input handling. These extensions demonstrate the plugin architecture and provide commonly needed functionality that can be enabled or disabled based on deployment requirements.

## Configuration Management

### Environment Variable System
The architecture supports extensive configuration through environment variables that control various aspects of system behavior. This includes display resolution settings, memory monitoring thresholds, input device configurations, and debugging options.

### Runtime Configuration
Many system parameters can be adjusted at runtime through the API interfaces, allowing for dynamic adaptation to changing system conditions and requirements. This flexibility is essential for supporting different use cases and hardware configurations within the same codebase.

## Performance Considerations

### Frame Rate Management
The architecture is designed around maintaining consistent frame rates through careful timing and resource management. The main loop includes sophisticated timing logic that adapts to system load while maintaining smooth visual output.

### Efficient Event Processing
Input event processing is optimized to minimize latency while supporting complex routing scenarios. The system uses efficient data structures and algorithms to ensure that input responsiveness is maintained even with multiple applications and complex key intercept configurations.

### Graphics Pipeline Optimization
The rendering pipeline is optimized for the specific requirements of set-top box and smart TV applications, with careful attention to memory bandwidth and GPU utilization patterns typical in these environments.
