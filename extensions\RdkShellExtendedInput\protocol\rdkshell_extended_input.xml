<?xml version="1.0" encoding="UTF-8"?>
<!--
    If not stated otherwise in this file or this component's LICENSE
    file the following copyright and licenses apply:

    Copyright 2020 RDK Management

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    * http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<protocol name="rdkshell_extended_input">

    <copyright>
        Copyright 2020 RDK Management
    </copyright>

    <interface name="rdkshell_extended_input" version="1">

        <description summary="The rdkshell_extended_input interface is used for receiving input from RCU devices">
            This is an optional alternative to the wl_keyboard interface, it was added so we could attach more meta data
            about the source of the event.

        </description>

        <enum name="key_state">
            <description summary="Describes the physical or virtual state of the key that produced the key event">
                There is not key repeat event, instead the shell will send multiple pressed events for the key repeat.
                Virtual key events are generated by the gesture recogniser if it is enabled for the given app.
            </description>
            <entry name="released" value="0" summary="The key has been released"/>
            <entry name="pressed" value="1" summary="The key has been pressed (you'll also get this for key repeats)"/>
            <entry name="virtual_release" value="2" summary="The virtual key has been released (you'll also get this for key repeats) - produced by gesture recogniser"/>
            <entry name="virtual_press" value="3" summary="The virtual key has been pressed - produced by gesture recogniser"/>
        </enum>

        <enum name="touchpad_state">
            <description summary="Describes the state of the touch pad when the event was generated">
                This is a tri-state describing the state of the touch pad at the time that event was generated.
            </description>
            <entry name="up" value="0" summary="end of touch event"/>
            <entry name="down" value="1" summary="the start of a touch event"/>
            <entry name="click" value="2" summary="the button beneath the touch pad has been pressed"/>
        </enum>

        <enum name="slider_state">
            <description summary="Describes the state of the slider touch pad when the event was generated">
                Describes the state of the slider touchpad when the event was generated.
            </description>
            <entry name="up" value="0" summary="end of touch event"/>
            <entry name="down" value="1" summary="the start of a touch event"/>
        </enum>

        <event name="key">
            <description summary="Key event">
                Event generated when a key is pressed, released or repeated. Also sent for virtual key events from the
                gesture recogniser if enabled.
                This event only ever generates keys using the standard linux key code values.
            </description>
            <arg name="serial" type="uint" summary="serial number of the key event"/>
            <arg name="time" type="uint" summary="timestamp with millisecond granularity"/>
            <arg name="device_id" type="uint" summary="the id of the device that produced the key event"/>
            <arg name="key" type="int" summary="key that produced the event"/>
            <arg name="state" type="uint" enum="key_state" summary="physical or virtual state of the key"/>
        </event>

        <event name="touchpad">
            <description summary="sends positional information and depression state of the touchpad">
                Sends the X and Y position and state of a touch event for the specified device to the listener.
            </description>
            <arg name="serial" type="uint" summary="serial number of the touch event"/>
            <arg name="time" type="uint" summary="timestamp with millisecond granularity"/>
            <arg name="device_id" type="uint" summary="the id of the device that produced the touch event"/>
            <arg name="x" type="int" summary="screen-local x coordinate"/>
            <arg name="y" type="int" summary="screen-local y coordinate"/>
            <arg name="state" type="uint" enum="touchpad_state" summary="the state of the touchpad when event was generated"/>
        </event>

        <event name="slider">
            <description summary="sends slider information">
                Sends the X position and state of a touch event for the specified device to the listener
            </description>
            <arg name="serial" type="uint" summary="serial number of the slider event"/>
            <arg name="time" type="uint" summary="timestamp with millisecond granularity"/>
            <arg name="device_id" type="uint" summary="the id of the device that produced the slider event"/>
            <arg name="x" type="int" summary="screen-local x coordinate"/>
            <arg name="state" type="uint" enum="slider_state" summary="the state of the slider when event was generated"/>
        </event>

    </interface>

</protocol>