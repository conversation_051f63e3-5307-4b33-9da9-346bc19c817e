/**
* If not stated otherwise in this file or this component's LICENSE
* file the following copyright and licenses apply:
*
* Copyright 2020 RDK Management
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
**/

#pragma once

#include <string>
#include <vector>

namespace RdkShell
{
    struct LinuxInputDevice
    {
        LinuxInputDevice() : vendor(0), product(0), deviceType(0), deviceMode(0), devicePath{} {}
        uint16_t vendor;
        uint16_t product;
        uint8_t deviceType;
        uint8_t deviceMode;
        std::string devicePath;
    };

    struct IrInputDeviceTypeMapping
    {
        IrInputDeviceTypeMapping() : filterCode(0), deviceType(0) {}
        uint8_t filterCode;
        uint8_t deviceType;
    };

    void readInputDevicesConfiguration();
    void inputDeviceTypeAndMode(const uint16_t vendor, const uint16_t product, const std::string& devicePath, uint8_t& type, uint8_t& mode);
    void irDeviceType(const uint8_t filterCode, uint8_t& type);
}
