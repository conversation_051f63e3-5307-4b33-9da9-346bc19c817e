# If not stated otherwise in this file or this component's license file the
# following copyright and licenses apply:
#
# Copyright 2020 RDK Management
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# below is a sample inputdevices.conf file

{
    "inputDevices":
    [
        {"vendor": "0x119b", "product":"0x2101", "deviceType":"0x00", "deviceMode": "0x00"},
        {"vendor": "0x119b", "product":"0x212b", "deviceType":"0x01", "deviceMode": "0x0f"},
        {"vendor": "0x06e7", "product":"0x8038", "deviceType":"0x02", "deviceMode": "0x03"},
        {"vendor": "0x06e7", "product":"0x8070", "deviceType":"0x04", "deviceMode": "0x03"},
        {"vendor": "0x06e7", "product":"0x8138", "deviceType":"0x06", "deviceMode": "0x03"},
        {"vendor": "0x06e7", "product":"0x8151", "deviceType":"0x07", "deviceMode": "0x03"},
        {"vendor": "0x057a", "product":"0x0004", "deviceType":"0x03", "deviceMode": "0x03"},
        {"vendor": "0x057a", "product":"0x0023", "deviceType":"0x05", "deviceMode": "0x03"}
    ],

    "irInputDeviceTypeMapping":
    [
        {"filterCode":19, "deviceType":"0xf2"},
        {"filterCode":20, "deviceType":"0xf1"},
        {"filterCode":21, "deviceType":"0xf3"}
    ]

}
