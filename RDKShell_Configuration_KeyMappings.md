# RDKShell Configuration and Key Mappings

## Configuration Overview

RDKShell provides extensive configuration capabilities through environment variables, configuration files, and compile-time options. The configuration system is designed to support both development scenarios with detailed debugging capabilities and production deployments with optimized performance characteristics.

## Environment Variables

### Core System Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `RDKSHELL_LOG_LEVEL` | string | "Information" | Sets the verbosity level for logging output. Valid values are "Debug", "Information", "Warn", "Error", and "Fatal". When set to "Debug", detailed runtime information is printed to help with development and troubleshooting. |
| `RDKSHELL_FRAMERATE` | integer | 40 | Controls the target frame rate for the main rendering loop. Higher values provide smoother animation but consume more CPU resources. The system will attempt to maintain this frame rate while processing input events and updating application states. |
| `RDKSHELL_ENABLE_IPC` | boolean | "0" | Enables the socket-based IPC communication system when set to "1". This allows external applications to communicate with RD<PERSON><PERSON> through JSON-RPC over Unix domain sockets. |
| `RDKSHELL_ENABLE_WS_IPC` | boolean | "0" | Enables the WebSocket-based IPC communication system when set to "1". This provides real-time bidirectional communication capabilities for web-based applications and modern client frameworks. |

### Memory Management Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `RDKSHELL_LOW_MEMORY_THRESHOLD` | double | 200.0 | Sets the threshold in megabytes for low memory notifications. When available system memory falls below this threshold, RDKShell will send low memory notifications to registered applications, allowing them to free up resources proactively. |
| `RDKSHELL_CRITICALLY_LOW_MEMORY_THRESHOLD` | double | 100.0 | Defines the critically low memory threshold in megabytes. When system memory falls below this level, RDKShell will send critical memory notifications and may take more aggressive resource management actions. This value must be less than or equal to the low memory threshold. |
| `RDKSHELL_SWAP_MEMORY_INCREASE_THRESHOLD` | double | 50.0 | Sets the threshold in megabytes for swap memory increase notifications. When swap usage increases by more than this amount, applications will be notified of potential memory pressure conditions. |

### Input System Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `RDKSHELL_KEY_INITIAL_DELAY` | integer | 500 | Configures the initial delay in milliseconds before key repeat events begin. This affects how long a user must hold a key before it starts repeating, providing control over input responsiveness and preventing accidental repeated inputs. |
| `RDKSHELL_KEY_REPEAT_INTERVAL` | integer | 100 | Sets the interval in milliseconds between key repeat events once repeating has started. Lower values result in faster key repetition, while higher values provide more controlled input for navigation scenarios. |

### Display Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `RDKSHELL_SET_GRAPHICS_720` | boolean | "0" | Forces the graphics system to initialize in 720p mode (1280x720) when set to "1". This is useful for devices with limited graphics capabilities or when 720p output is specifically required. The system will initialize with these dimensions regardless of the display's native resolution. |
| `RDKSHELL_SHOW_SPLASH_SCREEN` | string | undefined | When defined, enables the splash screen functionality. The splash screen provides visual feedback during system initialization and can be customized with specific images or animations. |
| `RDKSHELL_DISABLE_SPLASH_SCREEN_FILE` | string | undefined | Specifies a file path that, when present, will disable the splash screen even if `RDKSHELL_SHOW_SPLASH_SCREEN` is set. This provides a mechanism for runtime control of splash screen behavior. |

## Configuration Files

### Input Device Configuration

**File Location:** `inputdevices.conf`

The input device configuration file defines how different input devices are recognized and handled by the system. This JSON-formatted file allows for precise control over input device behavior and mapping.

```json
{
    "inputDevices": [
        {
            "vendor": "0x119b",
            "product": "0x2101", 
            "deviceType": "0x00",
            "deviceMode": "0x00"
        },
        {
            "vendor": "0x119b",
            "product": "0x212b",
            "deviceType": "0x01", 
            "deviceMode": "0x0f"
        }
    ],
    "irInputDeviceTypeMapping": [
        {
            "filterCode": 19,
            "deviceType": "0xf2"
        },
        {
            "filterCode": 20,
            "deviceType": "0xf1"
        }
    ]
}
```

#### Input Device Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `vendor` | string | USB vendor ID in hexadecimal format. This identifies the manufacturer of the input device and is used for device-specific handling and configuration. |
| `product` | string | USB product ID in hexadecimal format. Combined with the vendor ID, this uniquely identifies the specific device model and determines appropriate input handling behavior. |
| `deviceType` | string | Device type classification in hexadecimal format. This determines how the device's input events are processed and which input handling routines are applied. |
| `deviceMode` | string | Device mode configuration in hexadecimal format. This controls specific operational characteristics of the device, such as key repeat behavior and input event filtering. |

### Permissions Configuration

**File Location:** `rdkshellPermissions.conf`

The permissions configuration file controls which applications can access specific RDKShell extensions and capabilities. This security mechanism ensures that only authorized applications can use advanced features.

```json
{
    "clients": [
        {
            "client": "trusted_application",
            "extensions": ["libwesteros_plugin_rdkshell_client_control.so"]
        }
    ],
    "default": {
        "extensions": []
    }
}
```

#### Permission Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `client` | string | Application identifier that matches the client name used in API calls. This must exactly match the identifier used when the application connects to RDKShell. |
| `extensions` | array | List of extension library names that the client is permitted to use. Each extension provides specific capabilities, and access is granted on a per-client basis for security. |
| `default.extensions` | array | Default extension permissions applied to clients not explicitly listed in the configuration. An empty array means no extensions are available by default, requiring explicit permission grants. |

## Key Mapping System

### Key Mapping Overview

RDKShell implements a comprehensive key mapping system that translates between different key code formats and provides sophisticated input event routing capabilities. The system supports both Wayland key codes for low-level input handling and RDKShell virtual key codes for application-level input processing.

### Standard Key Mappings

#### Alphanumeric Keys

| Key | Wayland Code | RDKShell Code | Description |
|-----|--------------|---------------|-------------|
| 0-9 | 11, 2-10 | 48-57 | Number keys zero through nine |
| A-Z | 30, 48, 46, 32, 18, 33, 34, 35, 23, 36, 37, 38, 50, 49, 24, 25, 16, 19, 31, 20, 22, 47, 17, 45, 21, 44 | 65-90 | Letter keys A through Z |

#### Function Keys

| Key | Wayland Code | RDKShell Code | Description |
|-----|--------------|---------------|-------------|
| F1-F12 | 59-68, 87-88 | 112-123 | Standard function keys |
| F13-F24 | 183-194 | 124-136 | Extended function keys |

#### Navigation Keys

| Key | Wayland Code | RDKShell Code | Description |
|-----|--------------|---------------|-------------|
| Up Arrow | 103 | 38 | Directional up navigation key |
| Down Arrow | 108 | 40 | Directional down navigation key |
| Left Arrow | 105 | 37 | Directional left navigation key |
| Right Arrow | 106 | 39 | Directional right navigation key |
| Home | 102 | 36 | Home navigation key |
| End | 107 | 35 | End navigation key |
| Page Up | 104 | 33 | Page up navigation key |
| Page Down | 109 | 34 | Page down navigation key |

#### Control and Modifier Keys

| Key | Wayland Code | RDKShell Code | Flag Value | Description |
|-----|--------------|---------------|------------|-------------|
| Escape | 1 | 27 | - | Escape key for canceling operations |
| Tab | 15 | 9 | - | Tab key for navigation and focus control |
| Enter | 28 | 13 | - | Enter key for confirmation and line breaks |
| Space | 57 | 32 | - | Space bar for text input and selection |
| Backspace | 14 | 8 | - | Backspace key for deleting characters |
| Left/Right Shift | 42/54 | 16 | 8 | Shift modifier keys |
| Left/Right Ctrl | 29/97 | 17 | 16 | Control modifier keys |
| Left/Right Alt | 56/100 | 18 | 32 | Alt modifier keys |

#### Special Media and Remote Control Keys

| Key | Wayland Code | RDKShell Code | Description |
|-----|--------------|---------------|-------------|
| Red | 0x190 | 405 | Red colored key typically found on remote controls |
| Green | 0x191 | 406 | Green colored key typically found on remote controls |
| Yellow | 0x18e | 403 | Yellow colored key typically found on remote controls |
| Blue | 0x18f | 404 | Blue colored key typically found on remote controls |
| Back | 158 | 407 | Back navigation key for returning to previous screens |
| Menu | 139 | 408 | Menu key for accessing application menus |
| Home Page | 172 | 409 | Home page key for returning to main interface |
| Volume Up | 115 | 175 | Volume increase key |
| Volume Down | 114 | 174 | Volume decrease key |
| Mute | 113 | 173 | Audio mute toggle key |
| Play/Pause | 164 | 227 | Media play/pause toggle key |
| Fast Forward | 208 | 223 | Media fast forward key |
| Rewind | 168 | 224 | Media rewind key |

### Modifier Key Flags

RDKShell uses flag values to represent modifier key states that can be combined with regular key codes to create complex key combinations.

| Modifier | Flag Value | Description |
|----------|------------|-------------|
| Shift | 8 | Shift key modifier for uppercase letters and symbol access |
| Control | 16 | Control key modifier for keyboard shortcuts and commands |
| Alt | 32 | Alt key modifier for alternative character input and shortcuts |
| Command | 64 | Command/Windows key modifier for system-level shortcuts |

### Input Event Processing

#### Key Event Types
RDKShell processes two primary types of key events: key press events and key release events. Each event includes the key code, modifier flags, and timing metadata.

#### Key Repeat Handling
The system supports configurable key repeat functionality with separate settings for initial delay and repeat interval. Key repeat events are marked with a special flag to distinguish them from initial key press events.

#### Event Routing and Interception
Applications can register to intercept specific key combinations even when they are not in focus. This enables global hotkey functionality and allows background applications to respond to specific input events.

### Mouse and Pointer Input

#### Mouse Button Mappings

| Button | Flag Value | Description |
|--------|------------|-------------|
| Left Button | 1 | Primary mouse button for selection and activation |
| Middle Button | 2 | Middle mouse button typically used for scrolling |
| Right Button | 4 | Secondary mouse button for context menus |

## Compile-Time Configuration Options

### Build Configuration Flags

| Option | Default | Description |
|--------|---------|-------------|
| `RDKSHELL_BUILD_APP` | ON | Controls whether the main RDKShell executable is built. When disabled, only the shared library is created, allowing for custom application integration scenarios. |
| `RDKSHELL_BUILD_WEBSOCKET_IPC` | OFF | Enables WebSocket-based IPC communication support. This adds dependencies on WebSocket libraries and provides real-time communication capabilities for modern web applications. |
| `RDKSHELL_BUILD_KEY_METADATA` | OFF | Enables extended key metadata support that provides additional information about input events, including timing data and device-specific metadata. |
| `RDKSHELL_BUILD_IPC` | ON | Enables traditional socket-based IPC communication. This is the primary communication mechanism for most RDKShell integrations and should typically remain enabled. |
| `RDKSHELL_BUILD_FORCE_1080` | OFF | Enables compile-time support for forcing 1080p resolution. When enabled, the system can be configured to always initialize in 1080p mode regardless of display capabilities. |
| `RDKSHELL_BUILD_ENABLE_KEYREPEATS` | OFF | Enables advanced key repeat functionality with configurable timing and behavior. This provides more sophisticated input handling for applications that require precise key repeat control. |

## Runtime Configuration

### Memory Monitor Configuration

The memory monitoring system can be configured at runtime through API calls, allowing for dynamic adjustment of monitoring parameters based on system conditions.

### Dynamic Display Configuration

Display parameters can be adjusted at runtime through the API system, allowing applications to adapt to changing display conditions or user preferences.

### Input Device Runtime Configuration

Input device behavior can be modified at runtime through the input management APIs, enabling dynamic adaptation to different input scenarios and user preferences.

## Configuration Best Practices

### Development Configuration
For development environments, enable debug logging and extended metadata collection to facilitate troubleshooting and performance analysis. Use higher frame rates for smoother development experience but be aware of increased resource consumption.

### Production Configuration
In production deployments, use optimized logging levels and carefully tuned memory thresholds based on the specific hardware platform and application requirements. Disable unnecessary features to minimize resource usage and potential security exposure.

### Security Considerations
Carefully configure the permissions system to ensure that only trusted applications have access to sensitive extensions and capabilities. Regularly review and update permission configurations as applications are added or removed from the system.
