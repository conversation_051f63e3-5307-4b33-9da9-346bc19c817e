/**
* If not stated otherwise in this file or this component's LICENSE
* file the following copyright and licenses apply:
*
* Copyright 2020 RDK Management
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
**/

#pragma once

namespace RdkShell
{
    struct RdkShellRect
    {
        public:
            RdkShellRect(): x(0), y(0), width(0), height(0) {}
            RdkShellRect(uint32_t xval, uint32_t yval, uint32_t w, uint32_t h):x(xval), y(yval), width(w), height(h) {} 
            uint32_t x;
            uint32_t y;
            uint32_t width;
            uint32_t height;
    };
}
